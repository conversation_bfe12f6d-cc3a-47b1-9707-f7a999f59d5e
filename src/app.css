@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@400;500;600;700&display=swap');
@import 'tailwindcss';

@theme {
  --color-primary: #7C3AED;
  --color-primary-hover: #6D28D9;
  --color-heading: #0F172A;
  --color-text: #1E293B;
  --color-background: #F8FAFC;
  --color-subtle: #E2E8F0;
  --color-subtle-hover: #CBD5E1;

  --font-sans: "Inter", sans-serif;
  --font-display: "Space Grotesk", "Inter", sans-serif;
}

/* Utility classes for custom colors */
.bg-primary { background-color: var(--color-primary); }
.bg-primary-hover { background-color: var(--color-primary-hover); }
.bg-background { background-color: var(--color-background); }
.text-primary { color: var(--color-primary); }
.text-heading { color: var(--color-heading); }
.text-content { color: var(--color-text); }
.border-subtle { border-color: var(--color-subtle); }
.border-primary { border-color: var(--color-primary); }

/* Font utility classes */
.font-display { font-family: var(--font-display); }
.font-sans { font-family: var(--font-sans); }

/* Hover states */
.hover\:bg-primary-hover:hover { background-color: var(--color-primary-hover); }
.hover\:text-primary:hover { color: var(--color-primary); }

/* Custom styles for full-page scroll experience */
html, body {
  height: 100%;
  font-family: var(--font-sans);
  background-color: var(--color-background);
  color: var(--color-text);
  scroll-behavior: auto; /* Let GSAP handle smooth scrolling */
}

/* Hide scrollbar while keeping scroll functionality */
html {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

html::-webkit-scrollbar {
  width: 0;
  height: 0;
  display: none; /* Chrome, Safari, Opera */
}

/* Prevent default scrolling during animations */
body.scrolling {
  overflow: hidden;
}

/* Custom animation classes for GSAP */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in {
  opacity: 0;
}

.stagger-item {
  opacity: 0;
  transform: translateY(20px);
}

/* Form styling enhancements */
input:focus, textarea:focus {
  outline: none;
}

/* Smooth transitions for form elements */
input, textarea, button {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for contact section if needed */
.contact-section {
  scrollbar-width: thin;
  scrollbar-color: var(--color-subtle) transparent;
}

.contact-section::-webkit-scrollbar {
  width: 6px;
}

.contact-section::-webkit-scrollbar-track {
  background: transparent;
}

.contact-section::-webkit-scrollbar-thumb {
  background-color: var(--color-subtle);
  border-radius: 3px;
}

/* Video background optimizations */
video {
  object-fit: cover;
  object-position: center;
}

/* Mobile video optimization - disable on small screens */
@media (max-width: 768px) {
  video {
    display: none;
  }

  /* Fallback gradient background for mobile */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 25%, #4c1d95 50%, #6b21a8 75%, #7c3aed 100%);
    z-index: -1;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Improve touch targets */
  button, a, input, textarea {
    min-height: 44px;
  }

  /* Better mobile typography */
  h1, h2, h3, h4, h5, h6 {
    line-height: 1.2;
  }

  /* Optimize mobile scrolling */
  body {
    -webkit-overflow-scrolling: touch;
  }

  /* Prevent zoom on input focus */
  input, textarea, select {
    font-size: 16px;
  }

  /* Mobile-friendly form elements */
  input[type="text"],
  input[type="email"],
  input[type="tel"],
  textarea {
    -webkit-appearance: none;
    border-radius: 8px;
  }
}

/* Touch-friendly interactions */
@media (hover: none) and (pointer: coarse) {
  /* Remove hover effects on touch devices */
  .hover\:scale-105:hover {
    transform: none;
  }

  .hover\:scale-\[1\.02\]:hover {
    transform: none;
  }

  /* Add active states for touch feedback */
  button:active {
    transform: scale(0.98);
  }
}

/* Glassmorphism enhancements */
.glassmorphism {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Enhanced text shadows for better readability */
.text-shadow-strong {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

.text-shadow-medium {
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.6);
}

/* Accessibility: Respect prefers-reduced-motion */
@media (prefers-reduced-motion: reduce) {
  video {
    display: none !important;
  }

  .animate-pulse {
    animation: none;
  }

  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mobile performance optimizations */
@media (max-width: 768px) {
  /* Reduce backdrop blur for better performance */
  .backdrop-blur-md {
    backdrop-filter: blur(8px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
  }

  /* Optimize shadows for mobile */
  .shadow-2xl {
    box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  }

  .shadow-xl {
    box-shadow: 0 8px 20px -3px rgba(0, 0, 0, 0.25), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .bg-white\/5 {
    background-color: rgba(255, 255, 255, 0.15);
  }

  .bg-white\/10 {
    background-color: rgba(255, 255, 255, 0.25);
  }

  .border-white\/10 {
    border-color: rgba(255, 255, 255, 0.3);
  }

  .border-white\/20 {
    border-color: rgba(255, 255, 255, 0.4);
  }
}

/* Dark mode optimizations for mobile */
@media (prefers-color-scheme: dark) and (max-width: 768px) {
  /* Enhance contrast for better mobile readability */
  .text-white\/90 {
    color: rgba(255, 255, 255, 0.95);
  }

  .text-white\/80 {
    color: rgba(255, 255, 255, 0.9);
  }
}
